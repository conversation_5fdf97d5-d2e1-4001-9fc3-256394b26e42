#!/usr/bin/env node

// CORS Anywhere proxy server for ZapGap demo
// This server helps bypass CORS restrictions when calling the Langflow API

const corsAnywhere = require('cors-anywhere');

const host = process.env.HOST || '0.0.0.0';
const port = process.env.PORT || 8081;

// Create CORS Anywhere server
const server = corsAnywhere.createServer({
  originWhitelist: [
    'http://localhost:8080',
    'http://localhost:3000',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:3000',
    'https://zapgap-app.vercel.app',
    'https://zapgap.ai'
  ], // Allow specific origins
  requireHeaders: [], // No required headers
  removeHeaders: [
    'cookie',
    'cookie2',
    // Remove headers that might cause issues
  ],
  redirectSameOrigin: true,
  httpProxyOptions: {
    // Additional proxy options
    xfwd: false,
  },
});

// Start the server
server.listen(port, host, function() {
  console.log(`🚀 CORS Anywhere proxy server running on ${host}:${port}`);
  console.log('📡 Ready to proxy requests to Langflow API');
  console.log('🔗 Use: http://localhost:' + port + '/[target-url]');
  console.log('');
  console.log('Example usage:');
  console.log(`http://localhost:${port}/https://api.langflow.astra.datastax.com/...`);
});

// Handle graceful shutdown
process.on('SIGINT', function() {
  console.log('\n🛑 CORS proxy server shutting down...');
  server.close(function() {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', function() {
  console.log('\n🛑 CORS proxy server shutting down...');
  server.close(function() {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
