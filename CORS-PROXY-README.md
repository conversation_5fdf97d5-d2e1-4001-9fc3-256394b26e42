# CORS Proxy Setup for ZapGap Demo

This document explains the CORS Anywhere proxy integration for the ZapGap demo to handle CORS restrictions when calling the Langflow API.

## Overview

The ZapGap demo uses a CORS proxy server to bypass browser CORS restrictions when making API calls to the Langflow endpoint. This is necessary because browsers block cross-origin requests to external APIs for security reasons.

## Setup

### 1. Dependencies Installed
- `cors-anywhere@0.4.4` - The CORS proxy server
- `concurrently@9.1.2` - To run multiple servers simultaneously

### 2. Files Added
- `cors-proxy-server.cjs` - The CORS proxy server configuration
- Updated `package.json` with new scripts

### 3. Package.json Scripts
```json
{
  "cors-proxy": "node cors-proxy-server.cjs",
  "dev:full": "concurrently \"bun run cors-proxy\" \"bun run dev\""
}
```

## Usage

### Option 1: Run Both Servers Together (Recommended)
```bash
bun run dev:full
```
This starts both:
- CORS proxy server on `http://localhost:8081`
- Vite development server on `http://localhost:8080`

### Option 2: Run Servers Separately
```bash
# Terminal 1: Start CORS proxy
bun run cors-proxy

# Terminal 2: Start development server
bun run dev
```

## How It Works

1. **API Configuration**: The Demo component tries two endpoints:
   - Primary: `http://localhost:8081/https://api.langflow.astra.datastax.com/...` (via CORS proxy)
   - Fallback: `https://api.langflow.astra.datastax.com/...` (direct call)

2. **Automatic Fallback**: If the CORS proxy fails, the app automatically tries the direct API call

3. **Error Handling**: Comprehensive error handling for both network failures and API errors

## CORS Proxy Configuration

The proxy server is configured with:
- **Host**: `0.0.0.0:8081`
- **Origin Whitelist**: 
  - `http://localhost:8080` (development)
  - `http://localhost:3000` (alternative dev port)
  - `https://zapgap-app.vercel.app` (production)
  - `https://zapgap.ai` (custom domain)

## Security Notes

- The CORS proxy is only for development/demo purposes
- In production, implement proper CORS headers on the API server
- The proxy removes sensitive headers like cookies for security

## Troubleshooting

### CORS Proxy Not Starting
- Ensure port 8081 is available
- Check that `cors-anywhere` is installed: `bun add cors-anywhere`

### API Calls Still Failing
- Check browser console for detailed error messages
- Verify the Langflow API endpoint and token are correct
- Ensure both servers are running

### Port Conflicts
- Change the proxy port in `cors-proxy-server.cjs` if needed
- Update the `corsProxyEndpoint` in `Demo.tsx` accordingly

## Development vs Production

- **Development**: Uses CORS proxy for local testing
- **Production**: Should use proper CORS configuration on the API server
- The demo automatically falls back to direct calls if proxy is unavailable
